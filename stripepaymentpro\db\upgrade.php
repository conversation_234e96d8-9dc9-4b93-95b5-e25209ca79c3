<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON>od<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * database code for changing schema
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
function xmldb_enrol_stripepaymentpro_upgrade($oldversion) {
    global $DB;

    $dbman = $DB->get_manager();

    if ($oldversion < **********) {

        // Define table enrol_stripepro_coupons to be created.
        $table = new xmldb_table('enrol_stripepro_coupons');

        // Adding fields to table enrol_stripepro_coupons.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('couponid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('coupon_name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('amount_off', XMLDB_TYPE_FLOAT, '20,2', null, false);
        $table->add_field('percent_off', XMLDB_TYPE_FLOAT, '20,2', null, false);
        $table->add_field('currency', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('duration', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('no_of_months', XMLDB_TYPE_INTEGER, '10', null, false);
        $table->add_field('stripe_product_id', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '20', null, XMLDB_NOTNULL, null, null, null, 0);
        $table->add_field('coupon_expiry', XMLDB_TYPE_INTEGER, '20', null, XMLDB_NOTNULL, null, null, null, 0);

        // Adding keys to table enrol_stripepro_coupons.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('unique_coupon_course', XMLDB_KEY_UNIQUE, ['couponid']);

        // Conditionally launch create table for enrol_stripepro_coupons.
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Stripe savepoint reached.
        upgrade_plugin_savepoint(true, **********, 'enrol', 'stripepaymentpro');
    }

    return true;
}